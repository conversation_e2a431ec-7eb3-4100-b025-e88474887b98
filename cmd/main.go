package main

import (
	"context"
	"fancygame/sinksrv/internal/service"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/driver"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"

	"runtime"
)

type sinkService struct {
	Name string
	La   *service.LogAlertImpl
	Ctx  context.Context
}

func (l *sinkService) Init() error {
	l.Ctx = context.Background()
	l.Name = viper.GetString(dict.ConfigRpcServerName)
	l.La = service.NewLogAlert()
	logrus.Infoln(l.Name + "服务Init")

	return nil
}

func (l *sinkService) Start() error {
	return l.La.Consume(context.Background())
}

func (l *sinkService) Stop() error {
	logrus.Errorf(l.Name + "服务关闭中...")

	return nil
}

func (l *sinkService) ForceStop() error {
	logrus.Infoln(l.Name + " ForceStop ...")
	return nil
}

func main() {
	runtime.GOMAXPROCS(runtime.NumCPU() * 2)
	random.NewInitSource()

	driver.Run(&sinkService{})
}
