package test

import (
	"encoding/json"
	"fancygame/sinksrv/internal/server"
	"fmt"
	"testing"
)

// TestFeishuMessageFormat 测试飞书消息格式是否正确
func TestFeishuMessageFormat(t *testing.T) {
	// 测试消息模板
	temp := `{
    "msg_type": "interactive",
    "card": {
        "config": {
            "wide_screen_mode": true,
            "enable_forward": true
        },
        "elements": [
            {
                "tag": "div",
                "text": {
                    "content": "**时间:** %s",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "div",
                "text": {
                    "content": "**文件:** %s",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "div",
                "text": {
                    "content": "**函数:** %s",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "div",
                "text": {
                    "content": "**内容:** %s",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "div",
                "text": {
                    "content": "**详情:** %s",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "hr"
            }
        ],
        "header": {
            "template": "red",
            "title": {
                "tag": "plain_text",
                "content": "%s"
            }
        }
    }
}`

	// 测试数据
	timestamp := "2025-08-05T20:59:35.686559+08:00"
	file := "internal/server/server.go:29"
	funcName := "SendFeishuMsg"
	content := "AlertService.SendFeishuMsg success"
	detail := "测试详情信息"
	module := "sinksrv info"

	// 格式化消息
	pushMsg := fmt.Sprintf(temp, timestamp, file, funcName, content, detail, module)

	// 验证JSON格式是否正确
	var jsonData interface{}
	err := json.Unmarshal([]byte(pushMsg), &jsonData)
	if err != nil {
		t.Fatalf("JSON格式错误: %v\n消息内容: %s", err, pushMsg)
	}

	t.Logf("JSON格式验证通过")
	t.Logf("格式化后的消息: %s", pushMsg)
}

// TestSendFeishuMsg 测试发送飞书消息
func TestSendFeishuMsg(t *testing.T) {
	// 创建AlertService实例
	alertService := &server.AlertService{}

	// 测试消息模板
	temp := `{
    "msg_type": "interactive",
    "card": {
        "config": {
            "wide_screen_mode": true,
            "enable_forward": true
        },
        "elements": [
            {
                "tag": "div",
                "text": {
                    "content": "**时间:** %s",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "div",
                "text": {
                    "content": "**文件:** %s",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "div",
                "text": {
                    "content": "**函数:** %s",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "div",
                "text": {
                    "content": "**内容:** 测试消息发送",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "div",
                "text": {
                    "content": "**详情:** 这是一条测试消息",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "hr"
            }
        ],
        "header": {
            "template": "red",
            "title": {
                "tag": "plain_text",
                "content": "测试告警 info"
            }
        }
    }
}`

	// 测试数据
	timestamp := "2025-08-05T21:00:00.000000+08:00"
	file := "test/feishu_test.go:123"
	funcName := "TestSendFeishuMsg"

	// 格式化消息
	pushMsg := fmt.Sprintf(temp, timestamp, file, funcName)

	// 飞书机器人URL（请替换为你的实际URL）
	fsUrl := "https://open.feishu.cn/open-apis/bot/v2/hook/8c085321-498a-4bdc-bc90-ac8bf7d2ebf0"

	// 发送消息
	alertService.SendFeishuMsg(pushMsg, fsUrl)

	t.Logf("测试消息已发送")
}
