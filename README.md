# SinkSrv - 同步数据到存储服务

SinkSrv 是一个基于 Go 语言开发的同步数据到存储服务。

## 软件架构

本项目流程，见下图。临时存储也可替换为Redis，MongoDB：

![技术思维导图](./docs/images/SinkSrv架构.jpg)

更多请参考：[tech_share](https://tringame.feishu.cn/docx/OsiidKM9Wo5PdRxpIuichiLJn5c)

## 快速开始
参考cmd目录下的服务，如需要扩展可以实现Sinker对应接口
1. 启动生产者mock/event_test/docker-compose.yml，后运行sender_test
2. 启动消费者cmd/base_sink/main

### 依赖检查
1. 消费者依赖 mysql
2. 如需要启动生产者mock数据，需要安装docker-compose，相关代码在mock目录
 

> docker-compose基于 MacOs 编写，其他操作系统需要修改镜像为amd之类