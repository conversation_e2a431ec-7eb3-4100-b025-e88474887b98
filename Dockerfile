FROM harbor.office.coinmoney.xyz/game/alipine_build:1.0.1-git  as builder

MAINTAINER tCloud

ENV GO111MODULE=on \
    CGO_ENABLED=1 \
    GOOS=linux \
    GOARCH=amd64 \
    GOPROXY=https://goproxy.cn,direct \
    GOPRIVATE=git.coinmoney.xyz/backend/go-sdk

ENV PROJECT=/data/sink

WORKDIR ${PROJECT}
COPY go.mod go.sum ./
RUN go mod download && go mod verify

COPY  . .

RUN  go build -tags musl -ldflags "-s -w" -o ./app ${PROJECT}/main.go

FROM alpine as final

# 时区设置成当前时区
RUN apk add --no-cache tzdata

ENV TZ="America/Sao_Paulo"

WORKDIR /opt/sink

# 将二进制文件从${PROJECT}目录复制到这里
COPY --from=builder /data/sinksrv/app .
COPY --from=builder /data/sinksrv/config.yml ./config.yml

RUN chmod 0700 ./app

EXPOSE 8001

CMD ["/opt/sinksrv/app" ,"--config=/opt/sinksrv/config.yml"]
