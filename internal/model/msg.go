package model

type TLogMsg struct {
	Module    string                 `json:"module"`
	Timestamp string                 `json:"timestamp"`
	Level     string                 `json:"level"`
	File      string                 `json:"file"`
	Target    string                 `json:"target"`
	Line      int                    `json:"line"`
	FuncName  string                 `json:"func_name"`
	Fields    map[string]interface{} `json:"fields"`
}
