package sink

import (
	"context"
	"fmt"
	"github.com/sirupsen/logrus"
	"reflect"
	"sinksrv/internal/user_game_record/data"
	"time"
)

// task 定时任务调度
func task(sinker Sinker) {
	structureName := reflect.TypeOf(sinker).Elem().Name()
	// 尝试获取分布式锁
	lockKey := fmt.Sprintf("sink-cron-lock:%s", structureName)
	lockValue := time.Now().String()

	lockSuccess, err := data.Rdb.SetNX(context.TODO(), lockKey, lockValue, 5*time.Second).Result()
	if err != nil {
		logrus.Errorf("%s 获取分布式锁失败，err=%s", structureName, err)
		return
	}
	if !lockSuccess {
		logrus.Infof("%s 已有其他节点获取到分布式锁，跳过执行", structureName)
		return
	}
	defer func() {
		logrus.Infof("%s 释放分布式锁", structureName)
		data.Rdb.Del(context.Background(), lockKey) // 释放分布式锁
	}()

	logrus.Infof("%s 获取到分布式锁，开始执行业务逻辑", structureName)
	err = sinker.PutS3(context.TODO())
	if err != nil {
		logrus.Error(err)
	}
	logrus.Infof("%s 完成业务逻辑", structureName)
}
