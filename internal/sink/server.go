package sink

import (
	"context"
	"github.com/robfig/cron/v3"
	"github.com/segmentio/kafka-go"
	"github.com/sirupsen/logrus"
	"time"
)

type Sinker interface {
	Before(ctx context.Context) error

	Cancel(ctx context.Context) error

	Consume(ctx context.Context) error

	InsertDB(ctx context.Context) error

	PutS3(ctx context.Context) error
}

type SinkerImpl struct {
	// kafka 相关配置
	KReceiver event.Receiver
	KDataCh   chan KData
	BatchSize int
	Ticker    time.Duration
}

// Start 开始主流程
func Start(ctx context.Context, sinker Sinker, spec string) {

	// 做一些初始化
	err := sinker.Before(ctx)
	if err != nil {
		panic(err)
	}

	// 定时任务 同步到S3
	c := cron.New()
	if _, err = c.AddFunc(spec, func() {
		task(sinker)
	}); err != nil {
		panic(err)
	}
	c.Start()

	// 异步消费到channel
	go func() {
		err = sinker.Consume(ctx)
		if err != nil {
			logrus.Errorln(err)
		}
	}()

	// 读取channel 插入数据库 提交偏移量
	err = sinker.InsertDB(ctx)
	if err != nil {
		logrus.Errorln(err)
	}

}

func (s *SinkerImpl) Before(ctx context.Context) error {
	logrus.Info("Before")

	return nil
}

func (s *SinkerImpl) Cancel(ctx context.Context) error {
	logrus.Info("Cancel")

	return nil
}

func (s *SinkerImpl) Consume(ctx context.Context) error {
	receiver, err := event.NewKafkaReceiver()
	if err != nil {
		logrus.Fatalf("Failed to create kafka receiver: %v", err)
	}
	s.KReceiver = receiver
	defer receiver.Close()

	// 消费到channel 使用goroutine避免阻塞
	readerCh := make(chan kafka.Message, 10000)
	go func() {
		for {
			msg, err := receiver.GetReceive().FetchMessage(context.Background())
			if err != nil {
				logrus.Errorf("Error reading message: %v", err)
				continue
			}
			readerCh <- msg
		}
	}()

	// 批量提交初始化
	// kdata := make([]Message, 0, s.BatchSize)
	kdata := make([][]byte, 0, s.BatchSize)

	processedMessages := 0
	ticker := time.NewTicker(s.Ticker)
	defer ticker.Stop()
	var msg kafka.Message

	for {
		select {
		case msg = <-readerCh:

			kdata = append(kdata, msg.Value)
			processedMessages++

			// 数量够
			if processedMessages >= s.BatchSize {
				s.KDataCh <- KData{
					Raw: kdata,
					Msg: msg,
				}
				// 重置
				kdata = make([][]byte, 0, s.BatchSize)
				processedMessages = 0
				ticker.Reset(s.Ticker)
			}
		case <-ticker.C:
			// 时间到数量大于0才处理
			if processedMessages == 0 {
				continue
			}

			s.KDataCh <- KData{
				Raw: kdata,
				Msg: msg,
			}
			// 重置
			kdata = make([][]byte, 0, s.BatchSize)
			processedMessages = 0
		}
	}
}

func (s *SinkerImpl) InsertDB(ctx context.Context) error {
	logrus.Info("InsertDB")

	return nil
}

func (s *SinkerImpl) PutS3(ctx context.Context) error {
	logrus.Info("PutS3")

	return nil
}

// mg 只是为了测试base_sink的打印转换
type mg struct {
	Topic         string
	Partition     int
	Offset        int64
	HighWaterMark int64
	Key           string
	Value         string
	Time          time.Time
}
