package sink

import (
	"github.com/segmentio/kafka-go"
	"sync"
)

// KData 管道中的元素
type KData struct {
	Raw [][]byte
	Msg kafka.Message
}

// RecordMessage 通用消息结构
type RecordMessage struct {
	// 基础数据
	ID         string `json:"id"`
	UserID     string `json:"user_id"`
	ProviderID string `json:"provider_id"`
	GameID     int    `json:"game_id"`
	State      int    `json:"state"`
	Result     int    `json:"result"`
	MerchantID string `json:"merchant_id"`

	// 资产
	Currency   string `json:"currency"`
	Amount     string `json:"amount"`
	PayOut     string `json:"pay_out"`
	Multiplier string `json:"multiplier"`
	Balance    string `json:"balance"`

	// 时间
	CreateAt int64 `json:"create_at"`
	UpdateAt int64 `json:"update_at"`

	// 个性化数据
	Detail map[string]interface{} `json:"detail"`
}

// RecordMessagePool 定义一个 sync.Pool 对象
var RecordMessagePool = sync.Pool{
	New: func() interface{} {
		return &RecordMessage{}
	},
}
