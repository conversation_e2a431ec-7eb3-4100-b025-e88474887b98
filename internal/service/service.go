package service

import (
	"context"
	"encoding/json"
	"fancygame/sinksrv/internal/model"
	"fancygame/sinksrv/internal/server"
	"fmt"
	"git.keepfancy.xyz/back-end/frameworks/kit/kafka/event"
	"log"

	"github.com/spf13/cast"
	"strconv"
)

type LogAlertImpl struct {
}

func NewLogAlert() *LogAlertImpl {
	return &LogAlertImpl{}

}

func (s *LogAlertImpl) Consume(ctx context.Context) error {
	receiver, err := event.NewKafkaReceiver(event.WithTopic("logdog_logs"))
	if err != nil {
		log.Fatalf("Failed to create kafka receiver: %v", err)
	}
	defer receiver.Close()
	if err = receiver.Receive(ctx, handel); err != nil {
		panic(err)
	}

	select {}

	return nil
}

func handel(ctx context.Context, evt event.Event) error {

	var msg model.TLogMsg
	if err := json.Unmarshal(evt.Value(), &msg); err != nil {
		return err
	}

	text := cast.ToString(msg.Fields["msg"])
	if text == "" {
		text = cast.ToString(msg.Fields["context"])
	}

	escaped := strconv.Quote(string(evt.Value()))
	if len(escaped) > 2 {
		escaped = escaped[1 : len(escaped)-1]
	}

	escapedText := strconv.Quote(text)
	if len(escapedText) > 2 {
		escapedText = escapedText[1 : len(escapedText)-1]
	}

	pushMsg := fmt.Sprintf(temp, msg.Timestamp, msg.File+":"+cast.ToString(msg.Line), msg.FuncName, escapedText, escaped, msg.Module+"  "+msg.Level)
	fsUrl := "https://open.feishu.cn/open-apis/bot/v2/hook/8c085321-498a-4bdc-bc90-ac8bf7d2ebf0"
	go (&server.AlertService{}).SendFeishuMsg(pushMsg, fsUrl)

	return nil
}

var temp = `{
    "msg_type": "interactive",
    "card": {
        "config": {
            "wide_screen_mode": true,
            "enable_forward": true
        },
        "elements": [
            {
                "tag": "div",
                "text": {
                    "content": "**时间:** %s",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "div",
                "text": {
                    "content": "**文件:** %s",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "div",
                "text": {
                    "content": "**函数:** %s",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "div",
                "text": {
                    "content": "**内容:** %s",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "div",
                "text": {
                    "content": "**详情:** %s",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "hr"
            }
        ],
        "header": {
            "template": "red",
            "title": {
                "tag": "plain_text",
                "content": "%s"
            }
        }
    }
}`
