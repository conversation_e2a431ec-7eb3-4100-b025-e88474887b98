package server

import (
	"bytes"
	"github.com/sirupsen/logrus"
	"io/ioutil"
	"net/http"
)

type AlertService struct{}

// SendFeishuMsg 发送飞书信息
func (service *AlertService) SendFeishuMsg(pushMsg string, url string) {
	if url == "" {
		logrus.Infof("AlertService SendFeishuMsg get feishu url empty")
		return
	}

	// 记录发送的消息内容用于调试
	logrus.Debugf("AlertService.SendFeishuMsg sending message: %s", pushMsg)

	resp, err := http.Post(url, "application/json", bytes.NewBuffer([]byte(pushMsg)))
	if err != nil {
		logrus.Errorf("AlertService.SendFeishuMsg http.Post failed, url:%s, err:%s, pushMsg:%s", url, err.Error(), pushMsg)
		return
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logrus.Errorf("AlertService.SendFeishuMsg read response body failed, url:%s, err:%s, pushMsg:%s", url, err.Error(), pushMsg)
		return
	}

	// 检查HTTP状态码
	if resp.StatusCode != 200 {
		logrus.Errorf("AlertService.SendFeishuMsg HTTP error, url:%s, status:%d, resp:%s, pushMsg:%s", url, resp.StatusCode, string(body), pushMsg)
		return
	}

	logrus.Infof("AlertService.SendFeishuMsg success, url:%s, resp:%s", url, string(body))
}
