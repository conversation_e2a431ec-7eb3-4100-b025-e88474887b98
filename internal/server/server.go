package server

import (
	"bytes"
	"github.com/sirupsen/logrus"
	"io/ioutil"
	"net/http"
)

type AlertService struct{}

// SendFeishuMsg 发送飞书信息
func (service *AlertService) SendFeishuMsg(pushMsg string, url string) {
	if url == "" {
		logrus.Infof("AlertService SendFeishuMsg get feishu url empty")
		return
	}
	resp, err := http.Post(url, "application/json", bytes.NewBuffer([]byte(pushMsg)))
	if err != nil {
		logrus.Printf("AlertService.SendFeishuMsg http.Post url:%s,err:%s,resp:%+v,pushMsg:%s", url, err.Error(), resp, pushMsg)
		return
	}
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logrus.Infof("AlertService.SendFeishuMsg http.Post url:%s,err:%s,resp.Body:%+v,pushMsg:%s", url, err.Error(), resp.Body, pushMsg)
		return
	}

	logrus.Infof("AlertService.SendFeishuMsg success,url:%s,resp:%s", url, string(body))
}
